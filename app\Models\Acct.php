<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Acct extends Model
{
    use HasFactory;
    use softDeletes;


    protected $guarded = [];
    protected $table = 'acct';
    protected $primaryKey = 'AcctId';

    protected $fillable = [
        'AcctNm', 'ManufLocID', 'CntryCd', 'AcctURL', 'AcctStatId',
        'AcctTypeId', 'AcctMgrId', 'ShipViaId', 'Comm', 'location',
        'phoneNum', 'StaxCustomerId', 'StripeCustomerId'
    ];

    public function country(){
        return $this->belongsTo(Cntry::class, 'CntryCd');
    }

    public function status()
    {
        return $this->belongsTo(ActStat::class, 'AcctStatId', 'AcctStatId');
    }
    public function accountManager()
    {
        return $this->belongsTo(AcctMgr::class, 'AcctMgrId', 'AcctMgrId');
    }
    public function type(){
        return $this->belongsTo(AcctType::class, 'AcctTypeId',  'AcctTypeId');
    }
    public function shipVia()
    {
        return $this->belongsTo(ShipVia::class, 'ShipViaId' , 'ShipViaId');
    }
    public function manufacturer()
    {
        return $this->belongsTo(Manufloc::class, 'ManufLocID' , 'ManufLocID');
    }
}
